import { useMemo, useState } from "react";
import { Search, MapPin, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { LocationList } from "./LocationList";
import { IRelatedLocation } from "@/types";

/**
 * SearchableLocationList - A reusable component that wraps LocationList with search functionality
 *
 * @param {Array} locations - Array of location objects to display and search through
 * @param {Function} onLocationClick - Callback function when a location is clicked
 * @returns {JSX.Element} Searchable location list component
 */


interface SearchableLocationListProps {
  locations: IRelatedLocation[];
  onLocationClick: (location: IRelatedLocation) => void;
}


export function SearchableLocationList({ locations, onLocationClick }: SearchableLocationListProps): JSX.Element {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedState, setSelectedState] = useState<string | null>(null);

  const statesWithCounts = useMemo(() => {
    if (!locations || locations.length === 0) return [];

    const stateMap = new Map<string, number>();
    locations.forEach((loc) => {
      const state = loc.province?.trim();
      if (state) {
        stateMap.set(state, (stateMap.get(state) || 0) + 1);
      }
    });

    return Array.from(stateMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [locations]);

  const filteredLocations = useMemo(() => {
    if (!locations || locations.length === 0) return [];

    let filtered = locations;

    if (selectedState) {
      filtered = filtered.filter((loc) => loc.province?.trim() === selectedState);
    }

    if (searchTerm.length >= 2) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter((loc) => {
        if (loc.name && loc.name.toLowerCase().includes(searchLower))
          return true;

        if (loc.address && loc.address.toLowerCase().includes(searchLower))
          return true;

        if (loc.city && loc.city.toLowerCase().includes(searchLower))
          return true;

        if (loc.contactName && loc.contactName.toLowerCase().includes(searchLower))
          return true;

        if (loc.identity && loc.identity.toLowerCase().includes(searchLower))
          return true;

        if (loc.phone && loc.phone.toLowerCase().includes(searchLower))
          return true;

        if (loc.flags) {
          return loc.flags.some(flag =>
            flag && flag.toString().toLowerCase().includes(searchLower)
          );
        }

        return false;
      });
    }

    return filtered;
  }, [locations, searchTerm, selectedState]);

  const handleStateSelect = (stateName: string) => {
    setSelectedState(stateName === selectedState ? null : stateName);
  };

  const clearFilters = () => {
    setSelectedState(null);
    setSearchTerm("");
  };

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search locations by name, address, city, contact, phone..."
          className="pl-9"
        />
      </div>

      {/* State Filter Buttons */}
      {statesWithCounts.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Filter by State/Province
            </h3>
            {(selectedState || searchTerm) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-8 px-2 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Clear Filters
              </Button>
            )}
          </div>

          <ScrollArea className="w-full">
            <div className="flex gap-2 pb-2">
              {statesWithCounts.map((state) => (
                <Badge
                  key={state.name}
                  variant={selectedState === state.name ? "default" : "secondary"}
                  className={`cursor-pointer whitespace-nowrap transition-all hover:scale-105 ${selectedState === state.name
                      ? "bg-primary text-primary-foreground shadow-md"
                      : "hover:bg-secondary/80"
                    }`}
                  onClick={() => handleStateSelect(state.name)}
                >
                  {state.name} ({state.count})
                </Badge>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Showing {filteredLocations.length} of {locations?.length || 0} locations
          {selectedState && (
            <span className="ml-1">
              in <strong>{selectedState}</strong>
            </span>
          )}
          {searchTerm.length >= 2 && (
            <span className="ml-1">
              matching "<strong>{searchTerm}</strong>"
            </span>
          )}
        </span>
      </div>

      {/* Location List */}
      {filteredLocations && filteredLocations.length > 0 ? (
        <LocationList locations={filteredLocations} onLocationClick={onLocationClick} />
      ) : (selectedState || searchTerm.length >= 2) ? (
        <div className="text-center py-8">
          <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">
            No locations found
            {selectedState && ` in ${selectedState}`}
            {searchTerm.length >= 2 && ` matching "${searchTerm}"`}
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your filters or search terms
          </p>
        </div>
      ) : locations && locations.length === 0 ? (
        <p className="text-muted-foreground">No Locations for this business.</p>
      ) : null}
    </div>
  );
}