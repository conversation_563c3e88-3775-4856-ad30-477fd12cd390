import React, { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import QRCode from 'qrcode.react'
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Building2, FileText, MapPin, Users, Wrench, Settings, Plus } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Trash2, Image } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import MapSelector from "@/components/map_selector";
import { BusinessList, SearchableLocationList, LocationsSection } from '@/components/customers';
import { ServiceHistoryTab, WorkOrdersTab } from '@/components/customers/tabs';

const L = window.L;

// Fix Leaflet icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});


export function CustomersPage() {
  const navigate = useNavigate();
  const { user } = useContext(ConfigContext)

  const {
    departments,
    cities,
    businesses,
    selectedProvince,
    selectedCity,
    setProvince,
    setCity,
    setupUser
  } = useContext(BusinessContext);

  const [selectedBusiness, setSelectedBusiness] = useState(null);

  useEffect(() => {
    setupUser(user)
  }, [user]);

  const handleProvinceClick = (province) => {
    setProvince(province);
  };

  const handleCityClick = (city) => {
    setCity(city);
  };

  const handleBusinessClick = (business) => {
    setSelectedBusiness(business);
  };

  const handleBackClick = () => {
    setSelectedBusiness(null);
  };

  return selectedBusiness ? (
    <BusinessItem user={user} business={selectedBusiness} handleBackClick={handleBackClick} />
  ) : (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-4">Customers</h1>
          <p className="text-muted-foreground mb-6">Manage your customer information here.</p>
        </div>
        <Button
          className="flex items-center gap-2"
          onClick={() => navigate('/new-customer')}
        >
          <Plus className="h-4 w-4" />
          Add Customer
        </Button>
      </div>

      {/* Province/State Cards Grid */}
      {departments && departments.length > 0 && !selectedProvince &&
        <div>
          <h2 className="text-xl font-semibold mb-4">Select a State</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {departments && departments.map((province) => (
              <Card
                key={province.name}
                className={`cursor-pointer transition-all hover:shadow-md ${selectedProvince === province ? 'border-primary bg-primary/5' : ''
                  }`}
                onClick={() => handleProvinceClick(province)}
              >
                <CardContent className="p-4 flex items-center justify-between">
                  <span className="font-medium">{province}</span>
                  {selectedProvince === province && (
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      }

      {/* City Cards Grid - Only show when a province is selected */}
      {selectedProvince && cities && cities.length > 0 && !selectedCity && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Customer Cities in {selectedProvince}</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setProvince(null)}
            >
              Back to States
            </Button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {cities.map((city) => (
              <Card
                key={city}
                className="cursor-pointer transition-all hover:shadow-md"
                onClick={() => handleCityClick(city)}
              >
                <CardContent className="p-4">
                  <span className="font-medium">{city}</span>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Display selected city's businesses */}
      {selectedCity && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Customers in {selectedCity}, {selectedProvince}</h2>
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCity(null)}
              >
                Back to Cities
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setCity(null);
                  setProvince(null);
                }}
              >
                Back to States
              </Button>
            </div>
          </div>
          <BusinessList businesses={businesses} onBusinessClick={handleBusinessClick} />
        </div>
      )}

      {/* Display selected province's businesses (when no city is selected) */}
      {selectedProvince && !selectedCity && businesses && businesses.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">All Customers in {selectedProvince}</h2>
          </div>
          <BusinessList businesses={businesses} onBusinessClick={handleBusinessClick} />
        </div>
      )}
    </div>
  );
}


function BusinessItem({ user, business, handleBackClick }) {

  const {
    provider,

  } = useContext(BusinessContext);

  const [selectedLocation, setSelectedLocation] = useState(null);

  let loc = selectedLocation || business.locations[0]

  // if the selected business has multiple locations show the LocationList
  if (business.locations && business.locations.length > 1 && !selectedLocation) {
    return <div className="space-y-8">
      <div className="flex items-center gap-4">
        <h3 className="text-lg font-medium mb-4">Select a location</h3>
      </div>
      <SearchableLocationList locations={business.locations} onLocationClick={(loc) => setSelectedLocation(loc)} />
    </div>
  }
  return (
    <div className="space-y-8">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={handleBackClick}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{business.name}</h1>
          <p className="text-muted-foreground">
            {business.city}, {business.province}
          </p>
        </div>
      </div>

      <Tabs defaultValue="info" className="w-full">
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="info" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span>Info</span>
          </TabsTrigger>
          <TabsTrigger value="location" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <span>Location</span>
          </TabsTrigger>
          <TabsTrigger value="team" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Team</span>
          </TabsTrigger>
          <TabsTrigger value="equipment" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            <span>Equipment</span>
          </TabsTrigger>
          {/*    <TabsTrigger value="service-history" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            <span>Service History</span>
          </TabsTrigger> */}
          <TabsTrigger value="workorders" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Work Orders</span>
          </TabsTrigger>

          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Business Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Business Name</p>
                  <p>{business.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Contact Email</p>
                  <p>{business.email || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phone Number</p>
                  <p>{business.phone || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Website</p>
                  <p>{business.website || 'Not provided'}</p>
                </div>
              </div>


            </CardContent>
          </Card>

          <LocationsSection user={user} business={business} provider={provider} setSelectedLocation={setSelectedLocation} />

        </TabsContent>

        <TabsContent value="location" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Location Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2  gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Address</p>
                  <p>{loc.address || 'Not provided'}</p>
                  <p className="text-sm font-medium text-muted-foreground my-2">City</p>
                  <p>{loc.city}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">State/Province</p>
                  <p>{loc.province}</p>
                  <p className="text-sm font-medium text-muted-foreground my-2">Postal Code</p>
                  <p>{loc.postalCode || 'Not provided'}</p>
                </div>

                <div >
                  <p className="text-sm font-medium text-muted-foreground mb-3">Location QR Code</p>
                  <p className="text-sm font-medium text-muted-foreground my-2">https://openzcan.com/loc/{loc.id}</p>
                  <div className="bg-white p-2 rounded-md">
                    <p className="text-sm font-bold text-center text-black">OpenZcan.com</p>
                    <p className="text-sm font-bold text-center text-black">Scan to start</p>
                    <QRCode value={JSON.stringify(`https://openzcan.com/loc/${loc.id}`)} size={300} />
                    <p className="text-sm font-bold text-center text-black">{business.name}</p>
                    <p className="text-sm font-bold text-center text-black">{loc.address}</p>
                  </div>
                  <div className="mt-2">
                    Scan with the OpenZcan app on a mobile device to start work at this location
                  </div>
                </div>

                <div className="mb-2">
                  <MapSelector title={null}
                    position={null}
                    location={loc} onMapClick={(e) => {
                      //console.log("map clicked", typeof e, e)
                      //setLatlng(e)
                    }} />
                </div>


              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="team" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Team Members</h3>
              <p className="text-muted-foreground">No team members found for this business.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="equipment" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Equipment</h3>
                <Button size="sm" className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Equipment
                </Button>
              </div>

              {loc.Equipment && loc.Equipment.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px]">Photo</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Brand</TableHead>
                      <TableHead>Model</TableHead>
                      <TableHead>Code ID</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loc.Equipment.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={item.photoUrl} alt={item.name} />
                            <AvatarFallback>
                              <Image className="h-4 w-4 text-muted-foreground" />
                            </AvatarFallback>
                          </Avatar>
                        </TableCell>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.brand}</TableCell>
                        <TableCell>{item.model}</TableCell>
                        <TableCell>{item.codeId}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6 border rounded-md bg-muted/10">
                  <div className="flex justify-center mb-2">
                    <Wrench className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <p className="text-muted-foreground mb-2">No equipment records found for this business.</p>
                  <Button size="sm" variant="outline" className="flex items-center gap-2 mx-auto">
                    <Plus className="h-4 w-4" />
                    Add First Equipment
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="service-history" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Service History</h3>
              <ServiceHistoryTab user={user} business={business} location={loc} provider={provider} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workorders" className="mt-6">
          <Card>
            <WorkOrdersTab user={user} business={business} location={loc} provider={provider} />
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Business Settings</h3>
              <p className="text-muted-foreground">Configure business settings here.</p>
            </CardContent>
          </Card>m
        </TabsContent>
      </Tabs>
    </div>
  );
}


